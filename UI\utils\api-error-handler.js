/**
 * API错误处理工具
 * 提供统一的错误处理和fallback机制
 */

/**
 * API调用包装器，提供统一的错误处理
 * @param {Function} apiCall - API调用函数
 * @param {Object} options - 配置选项
 * @param {Function} options.fallback - 失败时的fallback函数
 * @param {string} options.loadingTitle - 加载提示文本
 * @param {string} options.errorTitle - 错误提示文本
 * @param {boolean} options.showLoading - 是否显示加载动画
 * @param {boolean} options.showError - 是否显示错误提示
 * @returns {Promise} API调用结果
 */
export async function apiCallWrapper (apiCall, options = {}) {
  const {
    fallback = null,
    loadingTitle = '加载中...',
    errorTitle = '加载失败',
    showLoading = true,
    showError = true
  } = options;

  try {
    // 显示加载动画
    if (showLoading) {
      uni.showLoading({
        title: loadingTitle,
      });
    }

    // 执行API调用
    const response = await apiCall();

    // 隐藏加载动画
    if (showLoading) {
      uni.hideLoading();
    }

    // 检查响应是否成功
    if (response && response.success) {
      return response;
    } else {
      throw new Error(response?.msg || 'API调用失败');
    }
  } catch (error) {
    // 隐藏加载动画
    if (showLoading) {
      uni.hideLoading();
    }

    // 显示错误提示
    if (showError) {
      uni.showToast({
        title: errorTitle,
        icon: "none",
      });
    }

    // 执行fallback函数
    if (fallback && typeof fallback === 'function') {
      try {
        return await fallback();
      } catch (fallbackError) {
        throw fallbackError;
      }
    }

    // 重新抛出错误
    throw error;
  }
}

/**
 * 批量API调用包装器
 * @param {Array} apiCalls - API调用函数数组
 * @param {Object} options - 配置选项
 * @returns {Promise} 所有API调用结果
 */
export async function batchApiCallWrapper (apiCalls, options = {}) {
  const {
    loadingTitle = '加载数据中...',
    errorTitle = '部分数据加载失败',
    showLoading = true,
    showError = true,
    continueOnError = true // 是否在部分失败时继续
  } = options;

  try {
    if (showLoading) {
      uni.showLoading({
        title: loadingTitle,
      });
    }

    const results = [];
    const errors = [];

    // 并行执行所有API调用
    const promises = apiCalls.map(async (apiCall, index) => {
      try {
        const result = await apiCall();
        results[index] = result;
        return result;
      } catch (error) {
        errors[index] = error;
        if (!continueOnError) {
          throw error;
        }
        return null;
      }
    });

    await Promise.all(promises);

    if (showLoading) {
      uni.hideLoading();
    }

    // 如果有错误且需要显示
    if (errors.length > 0 && showError) {
      uni.showToast({
        title: errorTitle,
        icon: "none",
      });
    }

    return {
      results,
      errors,
      hasErrors: errors.length > 0
    };
  } catch (error) {
    if (showLoading) {
      uni.hideLoading();
    }

    if (showError) {
      uni.showToast({
        title: errorTitle,
        icon: "none",
      });
    }

    throw error;
  }
}

/**
 * 数据验证器
 * @param {*} data - 要验证的数据
 * @param {Object} schema - 验证规则
 * @returns {Object} 验证后的数据
 */
export function validateApiData (data, schema) {
  if (!data || typeof data !== 'object') {
    throw new Error('数据格式无效');
  }

  const result = {};

  for (const [key, rule] of Object.entries(schema)) {
    const value = data[key];

    // 检查必填字段
    if (rule.required && (value === undefined || value === null)) {
      throw new Error(`缺少必填字段: ${key}`);
    }

    // 使用默认值
    if (value === undefined || value === null) {
      result[key] = rule.default;
      continue;
    }

    // 类型检查
    if (rule.type && typeof value !== rule.type) {
      if (rule.type === 'number' && !isNaN(Number(value))) {
        result[key] = Number(value);
      } else {
        result[key] = rule.default;
      }
    } else {
      result[key] = value;
    }

    // 数据转换
    if (rule.transform && typeof rule.transform === 'function') {
      result[key] = rule.transform(result[key]);
    }
  }

  return result;
}

/**
 * 重试机制
 * @param {Function} apiCall - API调用函数
 * @param {Object} options - 重试配置
 * @returns {Promise} API调用结果
 */
export async function retryApiCall (apiCall, options = {}) {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    backoff = true // 是否使用指数退避
  } = options;

  let lastError;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error;

      if (attempt === maxRetries) {
        break;
      }

      // 计算延迟时间
      const delay = backoff ? retryDelay * Math.pow(2, attempt) : retryDelay;

      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}

/**
 * 网络状态检查
 * @returns {Promise<boolean>} 网络是否可用
 */
export function checkNetworkStatus () {
  return new Promise((resolve) => {
    uni.getNetworkType({
      success: (res) => {
        resolve(res.networkType !== 'none');
      },
      fail: () => {
        resolve(false);
      }
    });
  });
}

/**
 * 智能API调用器，结合重试和网络检查
 * @param {Function} apiCall - API调用函数
 * @param {Object} options - 配置选项
 * @returns {Promise} API调用结果
 */
export async function smartApiCall (apiCall, options = {}) {
  const {
    checkNetwork = true,
    retry = true,
    fallback = null,
    ...otherOptions
  } = options;

  // 检查网络状态
  if (checkNetwork) {
    const isNetworkAvailable = await checkNetworkStatus();
    if (!isNetworkAvailable) {
      const error = new Error('网络不可用');
      if (fallback) {
        return await fallback();
      }
      throw error;
    }
  }

  // 包装API调用
  const wrappedApiCall = retry
    ? () => retryApiCall(apiCall, options)
    : apiCall;

  return await apiCallWrapper(wrappedApiCall, {
    fallback,
    ...otherOptions
  });
}

/**
 * 常用的错误处理预设
 */
export const ErrorHandlerPresets = {
  // 静默加载（不显示loading和错误提示）
  silent: {
    showLoading: false,
    showError: false
  },

  // 快速加载（短时间loading）
  quick: {
    loadingTitle: '加载中...',
    showLoading: true,
    showError: true
  },

  // 重要操作（显示详细信息）
  important: {
    loadingTitle: '处理中，请稍候...',
    errorTitle: '操作失败，请重试',
    showLoading: true,
    showError: true
  },

  // 后台刷新（静默刷新数据）
  background: {
    showLoading: false,
    showError: false,
    retry: true,
    maxRetries: 2
  }
};

/**
 * 使用示例：
 *
 * // 基本用法
 * const result = await apiCallWrapper(
 *   () => getVideoDetail(videoId),
 *   {
 *     loadingTitle: '加载视频信息...',
 *     errorTitle: '视频加载失败'
 *   }
 * );
 *
 * // 使用预设
 * const result = await smartApiCall(
 *   () => queryVideos(params),
 *   {
 *     ...ErrorHandlerPresets.important
 *   }
 * );
 *
 * // 批量调用
 * const { results, errors } = await batchApiCallWrapper([
 *   () => getVideoDetail(videoId),
 *   () => getUserVideoProgress(videoId),
 *   () => getVideoComments(videoId)
 * ], ErrorHandlerPresets.quick);
 */
