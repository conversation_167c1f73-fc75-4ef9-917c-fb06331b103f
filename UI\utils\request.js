/**
 * 公共API请求模块 - 适配uni-app环境
 * 基于uni.request封装，提供统一的请求处理、错误处理和认证管理
 */

import adminAuthService from './adminAuthService.js'
import { getApiBaseURL, API_CONFIG } from './config.js'
import { showError } from './toast-manager.js'

/**
 * 显示错误消息
 * @param {string} message 错误消息
 */
function showError (message) {
  uni.showToast({
    title: message || '请求失败',
    icon: 'none',
    duration: 2000
  })
}

/**
 * 显示确认对话框
 * @param {string} content 对话框内容
 * @param {string} title 对话框标题
 * @returns {Promise<boolean>} 用户选择结果
 */
function showConfirm (content, title = '提示') {
  return new Promise((resolve) => {
    uni.showModal({
      title,
      content,
      confirmText: '确认',
      cancelText: '取消',
      success: (res) => {
        resolve(res.confirm)
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}

/**
 * 处理认证错误
 * @param {string} message 错误消息
 */
async function handleAuthError (message) {
  showError(message || '认证失败')

  // 延迟1秒后清除登录信息并跳转到登录页
  setTimeout(() => {
    adminAuthService.logout()
    adminAuthService.redirectToLogin()
  }, 1000)
}

/**
 * 处理网络错误
 * @param {Object} error 错误对象
 */
async function handleNetworkError (error) {
  const shouldRelogin = await showConfirm(
    '无法连接到服务器，是否重新登录？',
    '连接错误'
  )

  if (shouldRelogin) {
    adminAuthService.logout()
    adminAuthService.redirectToLogin()
    // 重新加载页面
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/login/index'
      })
    }, 100)
  } else {
    uni.showToast({
      title: '已取消重新登录',
      icon: 'none'
    })
  }
}

/**
 * 请求拦截器 - 处理请求前的配置
 * @param {Object} config 请求配置
 * @returns {Object} 处理后的请求配置
 */
function requestInterceptor (config) {
  // 设置基础URL
  if (!config.url.startsWith('http')) {
    config.url = getApiBaseURL() + config.url
  }

  // 设置默认请求头
  config.header = {
    ...API_CONFIG.DEFAULT_HEADERS,
    ...config.header
  }

  // 添加认证token
  const loginInfo = adminAuthService.getLoginInfo()
  if (loginInfo && loginInfo.accessToken) {
    // 使用真实的accessToken
    config.header['Authorization'] = `Bearer ${loginInfo.accessToken}`
  }

  // 设置超时时间
  config.timeout = config.timeout || API_CONFIG.TIMEOUT

  return config
}

/**
 * 响应拦截器 - 处理响应数据和错误
 * @param {Object} response uni.request的响应对象
 * @returns {Promise} 处理后的响应数据或错误
 */
function responseInterceptor (response) {
  const { data, statusCode } = response

  // HTTP状态码检查
  if (statusCode !== 200) {
    if (statusCode === 401) {
      handleAuthError('认证失败')
      return Promise.reject(new Error('认证失败'))
    } else if (statusCode >= 500) {
      showError('服务器错误')
      return Promise.reject(new Error('服务器错误'))
    } else {

      showError(`请求失败 (${statusCode})`)
      return Promise.reject(new Error(`请求失败 (${statusCode})`))
    }
  }

  // 业务逻辑状态码检查 - 处理新的API响应格式
  if (data && typeof data === 'object') {
    // 新的API格式: {success: boolean, code: number, msg: string, data: any}
    if (typeof data.success === 'boolean') {
      if (data.success) {
        // 成功响应，返回完整的响应对象，让调用方处理
        return Promise.resolve(data)
      } else {
        // 失败响应
        const errorMsg = data.msg || '请求失败'

        // 处理认证错误
        if (data.code === 401) {
          handleAuthError(errorMsg)
          return Promise.reject(new Error(errorMsg))
        }

        // 其他业务错误，不显示错误提示，让调用方处理
        return Promise.resolve(data) // 返回完整响应，让调用方判断success字段
      }
    }

    // 兼容旧的API格式
    const { code, msg, message } = data

    // 处理成功响应
    if (code === 200 || code === 0) {
      return Promise.resolve(data)
    }

    // 处理业务逻辑错误
    if (code === 500) {
      // 直接返回原始响应，让调用方处理错误消息
      return Promise.resolve(data)
    }

    // 处理认证错误
    if (code === 401) {
      handleAuthError(msg || message || '认证失败')
      return Promise.reject(new Error(msg || message || '认证失败'))
    }

    // 其他错误码
    const errorMsg = msg || message || '未知错误'
    showError('接口错误: ' + errorMsg)
    return Promise.reject(new Error(errorMsg))
  }

  // 如果没有标准的响应格式，直接返回数据
  return Promise.resolve(data)
}

/**
 * 核心请求方法
 * @param {Object} config 请求配置
 * @returns {Promise} 请求Promise
 */
function request (config) {
  return new Promise((resolve, reject) => {
    // 应用请求拦截器
    const processedConfig = requestInterceptor(config)

    // 发起请求
    uni.request({
      ...processedConfig,
      success: (response) => {
        // 应用响应拦截器
        responseInterceptor(response)
          .then(resolve)
          .catch(reject)
      },
      fail: (error) => {

        // 处理网络错误
        if (error.errMsg && error.errMsg.includes('timeout')) {
          showError('请求超时，请检查网络连接')
        } else if (error.errMsg && error.errMsg.includes('fail')) {
          handleNetworkError(error)
        } else {
          showError('请求配置错误: ' + (error.errMsg || error.message || '未知错误'))
        }

        reject(error)
      }
    })
  })
}

/**
 * GET请求
 * @param {string} url 请求URL
 * @param {Object} params 请求参数
 * @param {Object} config 额外配置
 * @returns {Promise} 请求Promise
 */
function get (url, params = {}, config = {}) {
  return request({
    url,
    method: 'GET',
    data: params,
    ...config
  })
}

/**
 * POST请求
 * @param {string} url 请求URL
 * @param {Object} data 请求数据
 * @param {Object} config 额外配置
 * @returns {Promise} 请求Promise
 */
function post (url, data = {}, config = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...config
  })
}

/**
 * PUT请求
 * @param {string} url 请求URL
 * @param {Object} data 请求数据
 * @param {Object} config 额外配置
 * @returns {Promise} 请求Promise
 */
function put (url, data = {}, config = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...config
  })
}

/**
 * DELETE请求
 * @param {string} url 请求URL
 * @param {Object} params 请求参数
 * @param {Object} config 额外配置
 * @returns {Promise} 请求Promise
 */
function del (url, params = {}, config = {}) {
  return request({
    url,
    method: 'DELETE',
    data: params,
    ...config
  })
}

// 导出API请求方法
export default {
  request,
  get,
  post,
  put,
  delete: del,
  // 工具方法
  getApiBaseURL,
  showError,
  showConfirm
}

// 单独导出各个方法，方便按需引入
export {
  request,
  get,
  post,
  put,
  del as delete,
  getApiBaseURL,
  showError,
  showConfirm
}
